import sys
import webbrowser
import json
import os
import re
import time
import datetime
from urllib.parse import urlparse, parse_qs, quote_plus, unquote_plus
from collections import deque

try:
    import requests
except ImportError:
    print("Error: The 'requests' library is not installed. Please install it using 'pip install requests'")
    sys.exit(1)

try:
    from PySide6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QTabWidget, QPushButton, QTextEdit, QLineEdit, QLabel,
        QFileDialog, QMessageBox, QScrollArea, QFrame, QSpacerItem,
        QSizePolicy, QPlainTextEdit, QComboBox, QDialog
    )
    from PySide6.QtCore import Qt, QThread, Signal, Slot, QSize
    from PySide6.QtGui import QClipboard, QIcon # QIcon is optional for window icon
except ImportError:
    print("Error: The 'PySide6' library is not installed. Please install it using 'pip install PySide6'")
    sys.exit(1)

# --- Configuration ---
ADJUST_WRAPPER_URL = "https://app.adjust.com/10y4cdbj"
BESITOS_API_BASE_URL = "https://wall.besitos.ai/api/v2"
BESITOS_PARTNER_ID = "m1n1dwunbl7w"
BESITOS_CHANNEL = "games"
MAX_LOG_ENTRIES = 150

# --- Modern Blue Theme QSS (Qt Style Sheet) ---
MODERN_THEME_QSS = """
    QMainWindow, QWidget {
        background-color: #1a1a2e;
        color: #eee;
        font-size: 10pt;
        font-family: "Segoe UI", Arial, sans-serif;
    }

    QTabWidget::pane {
        border-top: 3px solid #16213e;
        background-color: #1a1a2e;
        border-radius: 0px 0px 8px 8px;
    }

    QTabBar::tab {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #0f3460, stop: 1 #16213e);
        color: #eee;
        padding: 12px 24px;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        min-width: 140px;
        margin-right: 2px;
        font-weight: 500;
    }

    QTabBar::tab:selected {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #533483, stop: 1 #0f3460);
        color: #eee;
        font-weight: bold;
        border-bottom: 3px solid #e94560;
    }

    QTabBar::tab:hover:!selected {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #533483, stop: 1 #16213e);
    }

    QPushButton {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #533483, stop: 1 #0f3460);
        border: 2px solid #16213e;
        padding: 10px 18px;
        min-height: 24px;
        border-radius: 6px;
        color: #eee;
        font-weight: 600;
        font-size: 9pt;
    }

    QPushButton:hover {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #e94560, stop: 1 #533483);
        color: #eee;
        border: 2px solid #e94560;
    }

    QPushButton:pressed {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #0f3460, stop: 1 #16213e);
        color: #eee;
        border: 2px solid #1a1a2e;
    }

    QPushButton:disabled {
        background-color: #16213e;
        color: #0f3460;
        border: 2px solid #1a1a2e;
    }

    QLineEdit, QTextEdit, QPlainTextEdit {
        background-color: #16213e;
        border: 2px solid #0f3460;
        padding: 8px;
        border-radius: 6px;
        color: #eee;
        selection-background-color: #533483;
        selection-color: #eee;
    }

    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
        border: 2px solid #533483;
        background-color: #1a1a2e;
    }

    QTextEdit:read-only, QPlainTextEdit:read-only {
        background-color: #1a1a2e;
        border: 2px solid #16213e;
    }

    QLabel {
        color: #eee;
        font-weight: 500;
    }

    QScrollArea {
        border: 2px solid #16213e;
        background-color: #1a1a2e;
        border-radius: 6px;
    }

    QScrollBar:vertical {
        border: 1px solid #16213e;
        background: #1a1a2e;
        width: 16px;
        margin: 0px;
        border-radius: 8px;
    }

    QScrollBar::handle:vertical {
        background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                   stop: 0 #533483, stop: 1 #0f3460);
        min-height: 24px;
        border-radius: 6px;
        margin: 2px;
    }

    QScrollBar::handle:vertical:hover {
        background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                   stop: 0 #e94560, stop: 1 #533483);
    }

    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
        height: 0px;
    }

    QScrollBar:horizontal {
        border: 1px solid #16213e;
        background: #1a1a2e;
        height: 16px;
        margin: 0px;
        border-radius: 8px;
    }

    QScrollBar::handle:horizontal {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #533483, stop: 1 #0f3460);
        min-width: 24px;
        border-radius: 6px;
        margin: 2px;
    }

    QScrollBar::handle:horizontal:hover {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #e94560, stop: 1 #533483);
    }

    QGroupBox, QFrame[frameShape="StyledPanel"] {
        border: 2px solid #533483;
        border-radius: 8px;
        margin-top: 12px;
        padding: 12px;
        background-color: rgba(22, 33, 62, 0.3);
    }

    QGroupBox::title {
        subcontrol-origin: margin;
        subcontrol-position: top left;
        padding: 4px 8px;
        color: #eee;
        font-weight: bold;
        background-color: #533483;
        border-radius: 4px;
    }

    /* Specific styling for special elements */
    #ActivityLog, #AdjustUrlInput, #OfferJsonDisplay {
        font-family: "Consolas", "Courier New", monospace;
        font-size: 9pt;
        background-color: #16213e;
        border: 2px solid #0f3460;
    }

    /* Status message styling */
    .success-message {
        color: #eee;
        background-color: #0f3460;
        border: 1px solid #533483;
        border-radius: 4px;
        padding: 4px 8px;
    }

    .error-message {
        color: #eee;
        background-color: #16213e;
        border: 1px solid #1a1a2e;
        border-radius: 4px;
        padding: 4px 8px;
    }

    .warning-message {
        color: #1a1a2e;
        background-color: #e94560;
        border: 1px solid #533483;
        border-radius: 4px;
        padding: 4px 8px;
    }
"""

# --- Worker Threads ---
class GenericWorker(QThread):
    result = Signal(object)
    error = Signal(str)
    finished = Signal() # To re-enable buttons etc.

    def __init__(self, function, *args, **kwargs):
        super().__init__()
        self.function = function
        self.args = args
        self.kwargs = kwargs

    def run(self):
        try:
            res = self.function(*self.args, **self.kwargs)
            self.result.emit(res)
        except Exception as e:
            self.error.emit(str(e))
        finally:
            self.finished.emit()

# --- Main Application Window ---
class MobileMarketingToolkit(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚀 Mobile Marketing Toolkit - Professional Edition")
        self.setGeometry(100, 100, 1500, 950) # Increased size for better layout
        self.setStyleSheet(MODERN_THEME_QSS) # Apply modern theme

        # Central Widget and Tab Setup
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QHBoxLayout(self.central_widget) # Main layout is horizontal

        self.tab_widget = QTabWidget()
        self.main_layout.addWidget(self.tab_widget, 3) # Tabs take 3/4 of space

        # Activity Log (as a separate panel on the right)
        self._create_activity_log_panel()
        self.main_layout.addWidget(self.activity_log_group, 1) # Log takes 1/4 of space

        # Initialize Tabs
        self._create_adjust_toolkit_tab()
        self._create_offer_wall_tab()
        self._create_besitos_wall_tab()

        self._log_activity("Application started.", "INFO")

    def _create_activity_log_panel(self):
        self.activity_log_group = QFrame() # Using QFrame for styling flexibility
        self.activity_log_group.setObjectName("ActivityLogGroup")
        log_panel_layout = QVBoxLayout(self.activity_log_group)
        log_panel_layout.setContentsMargins(5,5,5,5)

        log_title = QLabel("📊 Activity Log")
        log_title.setStyleSheet("font-size: 13pt; font-weight: bold; color: #eee; background-color: #533483; padding: 8px; border-radius: 6px; margin-bottom: 8px;")
        log_panel_layout.addWidget(log_title)

        self.activity_log_text = QPlainTextEdit() # QPlainTextEdit is better for logs
        self.activity_log_text.setObjectName("ActivityLog")
        self.activity_log_text.setReadOnly(True)
        self.activity_log_text.setLineWrapMode(QPlainTextEdit.LineWrapMode.WidgetWidth)
        log_panel_layout.addWidget(self.activity_log_text)
        self.log_entries = deque(maxlen=MAX_LOG_ENTRIES)

    def _log_activity(self, message, level="INFO"):
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        
        self.log_entries.append(log_entry)
        
        # Update QPlainTextEdit efficiently
        self.activity_log_text.setPlainText("\n".join(self.log_entries))
        self.activity_log_text.verticalScrollBar().setValue(self.activity_log_text.verticalScrollBar().maximum()) # Scroll to bottom
        
        if level == "ERROR":
            print(f"ERROR: {message}")

    def _create_scrollable_area(self):
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area_content = QWidget()
        scroll_area.setWidget(scroll_area_content)
        content_layout = QVBoxLayout(scroll_area_content)
        content_layout.setAlignment(Qt.AlignmentFlag.AlignTop) # Content flows from top
        return scroll_area, content_layout

    # --- Adjust URL Toolkit Tab ---
    def _create_adjust_toolkit_tab(self):
        self.adjust_tab = QWidget()
        self.tab_widget.addTab(self.adjust_tab, "🔗 Adjust URL Toolkit")
        adjust_tab_layout = QVBoxLayout(self.adjust_tab)

        # Input Frame
        input_group = QFrame()
        input_group.setFrameShape(QFrame.Shape.StyledPanel)
        input_group_layout = QVBoxLayout(input_group)
        
        input_label = QLabel("Paste Adjust tracking links (one per line):")
        input_group_layout.addWidget(input_label)

        self.adjust_url_input = QTextEdit()
        self.adjust_url_input.setObjectName("AdjustUrlInput")
        self.adjust_url_input.setPlaceholderText("e.g., https://app.adjust.com/tracker?param=value...")
        self.adjust_url_input.setMinimumHeight(100)
        input_group_layout.addWidget(self.adjust_url_input)

        button_bar = QHBoxLayout()
        self.adj_paste_btn = QPushButton("📋 Paste from Clipboard")
        self.adj_paste_btn.clicked.connect(self.adj_paste_from_clipboard)
        button_bar.addWidget(self.adj_paste_btn)

        self.adj_clear_btn = QPushButton("🗑️ Clear Input & Results")
        self.adj_clear_btn.clicked.connect(self.adj_clear_input)
        button_bar.addWidget(self.adj_clear_btn)

        button_bar.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))

        self.adj_process_btn = QPushButton("⚡ Process URLs & Extract Callbacks")
        self.adj_process_btn.clicked.connect(self.adj_process_urls)
        button_bar.addWidget(self.adj_process_btn)
        input_group_layout.addLayout(button_bar)
        adjust_tab_layout.addWidget(input_group)

        # Results Frame
        results_group_title = QLabel("Processed Links & Callbacks")
        results_group_title.setStyleSheet("font-size: 12pt; font-weight: bold; margin-top: 15px; color: #eee; background-color: #0f3460; padding: 8px; border-radius: 6px;")
        adjust_tab_layout.addWidget(results_group_title)

        self.adjust_results_scroll_area, self.adjust_results_layout = self._create_scrollable_area()
        adjust_tab_layout.addWidget(self.adjust_results_scroll_area)

    def adj_paste_from_clipboard(self):
        clipboard = QApplication.clipboard()
        text = clipboard.text()
        if text:
            self.adjust_url_input.append(text) # append to add newline if needed
            self._log_activity("Pasted content from clipboard into Adjust URL input.", "SUCCESS")
            QMessageBox.information(self, "Paste Success", "Content pasted from clipboard.")
        else:
            self._log_activity("Clipboard is empty or contains no text.", "WARNING")
            QMessageBox.information(self, "Paste Info", "Clipboard is empty or contains no text.")

    def adj_clear_input(self):
        self.adjust_url_input.clear()
        self._clear_layout(self.adjust_results_layout)
        self._log_activity("Adjust URL input and results cleared.")

    def adj_process_urls(self):
        urls_text = self.adjust_url_input.toPlainText().strip()
        if not urls_text:
            QMessageBox.warning(self, "Input Required", "Please input Adjust URLs to process.")
            return

        input_urls = [url.strip() for url in urls_text.splitlines() if url.strip()]
        self._clear_layout(self.adjust_results_layout)
        self._log_activity(f"Starting processing of {len(input_urls)} Adjust URLs.")

        self.adj_process_btn.setEnabled(False)
        # Using a worker would be for a very long list, for now, direct processing with UI updates
        for i, original_url in enumerate(input_urls):
            self._display_single_adj_url_processing(original_url, i)
        self.adj_process_btn.setEnabled(True)
        self._log_activity("Finished processing all Adjust URLs.")

    def _display_single_adj_url_processing(self, original_url, index):
        url_frame = QFrame()
        url_frame.setFrameShape(QFrame.Shape.StyledPanel)
        url_frame_layout = QVBoxLayout(url_frame)

        url_frame_layout.addWidget(QLabel(f"<b>Original URL ({index+1}):</b> {original_url[:100]}{'...' if len(original_url) > 100 else ''}"))

        parsed_url = urlparse(original_url)
        if not (parsed_url.scheme in ['http', 'https'] and parsed_url.netloc):
            err_label = QLabel("Error: Invalid URL format.")
            err_label.setStyleSheet("color: #eee; background-color: #16213e; padding: 6px; border-radius: 4px; font-weight: bold;")
            url_frame_layout.addWidget(err_label)
            self._log_activity(f"URL '{original_url}' is invalid.", "ERROR")
            self.adjust_results_layout.addWidget(url_frame)
            return

        query_params = parse_qs(parsed_url.query)
        
        app_name = "Unknown App"
        adj_t_token = query_params.get('adj_t', [None])[0]
        if adj_t_token: app_name = f"App (token: {adj_t_token})"
        elif "adjust.com" in parsed_url.netloc and parsed_url.path and parsed_url.path != "/":
            app_name = parsed_url.path.split('/')[1] if len(parsed_url.path.split('/')) > 1 else parsed_url.netloc
        else: app_name = parsed_url.netloc
        url_frame_layout.addWidget(QLabel(f"Identified App/Game: {app_name}"))

        install_callback_val = query_params.get('install_callback', [None])[0]
        event_callbacks = {k: v[0] for k, v in query_params.items() if k.startswith('event_callback_')}
        generated_any_link = False
        event_link_details = []

        if install_callback_val:
            generated_any_link = True
            wrapped_link = f"{ADJUST_WRAPPER_URL}?click_callback={quote_plus(install_callback_val)}"
            self._create_pyside_link_display(url_frame_layout, "Install Callback Link", wrapped_link, "Install")
            self._log_activity(f"Generated install callback for {original_url}", "INFO")

        if event_callbacks:
            generated_any_link = True
            url_frame_layout.addWidget(QLabel("<b>Event Callback Links:</b>"))
            for param_name, callback_val in event_callbacks.items():
                event_id = param_name.replace('event_callback_', '')
                event_name_display = f"Event ({event_id if event_id else 'default'})"
                wrapped_link = f"{ADJUST_WRAPPER_URL}?click_callback={quote_plus(callback_val)}"
                event_link_details.append({"name": event_name_display, "link": wrapped_link})
                self._create_pyside_link_display(url_frame_layout, event_name_display, wrapped_link, "Event")
                self._log_activity(f"Generated {event_name_display} callback for {original_url}", "INFO")
            
            if len(event_link_details) > 1:
                fire_all_button = QPushButton("🎯 Fire All Events (Smart Order)")
                fire_all_button.clicked.connect(lambda checked=False, links=list(event_link_details): self.adj_fire_all_events(links))
                url_frame_layout.addWidget(fire_all_button, alignment=Qt.AlignmentFlag.AlignLeft)


        if not generated_any_link:
            no_cb_label = QLabel("No processable install or event callbacks found.")
            no_cb_label.setStyleSheet("color: #1a1a2e; background-color: #e94560; padding: 6px; border-radius: 4px; font-weight: bold;")
            url_frame_layout.addWidget(no_cb_label)
            self._log_activity(f"No callbacks found for URL '{original_url}'.", "WARNING")
        
        self.adjust_results_layout.addWidget(url_frame)

    def _create_pyside_link_display(self, parent_layout, link_name, link_url, link_type_prefix):
        link_group_layout = QHBoxLayout()
        link_group_layout.addWidget(QLabel(f"{link_name}:"))
        
        link_text = QLineEdit(link_url)
        link_text.setReadOnly(True)
        link_group_layout.addWidget(link_text, 1) # Expandable text field

        copy_button = QPushButton("📋 Copy")
        copy_button.setFixedWidth(90)
        copy_button.clicked.connect(lambda: self.copy_to_clipboard(link_url, f"{link_type_prefix} link"))
        link_group_layout.addWidget(copy_button)

        fire_button = QPushButton(f"🚀 Fire {link_type_prefix}")
        fire_button.setFixedWidth(120)
        fire_button.clicked.connect(lambda: self.fire_link(link_url, f"{link_type_prefix} link"))
        link_group_layout.addWidget(fire_button)
        parent_layout.addLayout(link_group_layout)

    def adj_fire_all_events(self, event_link_details):
        if not event_link_details: return
        num_events = len(event_link_details)
        ordered_links_to_fire = []
        N = min(2, num_events) # Number of last events to fire first, ensure N <= num_events

        if num_events <= N:
            ordered_links_to_fire = [details['link'] for details in reversed(event_link_details)]
        else:
            for i in range(num_events - 1, num_events - 1 - N, -1): # Last N events (reversed)
                ordered_links_to_fire.append(event_link_details[i]['link'])
            if 0 not in [num_events - 1 - j for j in range(N)]: # Ensure first element is not already added
                 ordered_links_to_fire.append(event_link_details[0]['link']) # First event
            
            # Middle events (excluding first and last N)
            start_middle = 1
            end_middle = num_events - N
            for i in range(start_middle, end_middle):
                 if event_link_details[i]['link'] not in ordered_links_to_fire:
                    ordered_links_to_fire.append(event_link_details[i]['link'])
        
        self._log_activity(f"Starting to fire {len(ordered_links_to_fire)} events in smart order.", "INFO")
        self.sequential_fire_worker = GenericWorker(self._fire_links_sequentially, ordered_links_to_fire, "Event")
        # self.sequential_fire_worker.finished.connect(lambda: self.adj_process_btn.setEnabled(True)) # If main btn disabled
        self.sequential_fire_worker.start()

    def _fire_links_sequentially(self, links, link_type_name):
        """Fire links sequentially using direct HTTP requests - FAST!"""
        delay_between_fires = 0.3  # Reduced to 0.3 seconds for faster firing

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }

        for i, link_url in enumerate(links):
            try:
                # Log detailed request information
                parsed_url = urlparse(link_url)
                query_params = parse_qs(parsed_url.query)

                # Extract and decode click_callback if present
                callback_info = "No callback data"
                if 'click_callback' in query_params:
                    callback_url = query_params['click_callback'][0]
                    callback_info = f"Callback URL: {callback_url}"

                    # Try to parse callback URL parameters
                    try:
                        callback_parsed = urlparse(callback_url)
                        callback_params = parse_qs(callback_parsed.query)
                        if callback_params:
                            param_details = []
                            for key, values in callback_params.items():
                                param_details.append(f"{key}={values[0] if values else 'None'}")
                            callback_info += f" | Params: {', '.join(param_details)}"
                    except:
                        pass

                # Extract click_id for logging
                click_id_info = ""
                if "click_id=" in link_url:
                    try:
                        click_id = query_params.get('click_id', [None])[0]
                        if click_id:
                            click_id_info = f" (Click ID: {click_id[:8]}...)"
                    except:
                        pass

                # Log the request being sent
                self._log_activity(
                    f"📤 SENDING {link_type_name} {i+1}/{len(links)}:\n"
                    f"  🎯 Target: {parsed_url.netloc}\n"
                    f"  📋 {callback_info[:150]}{'...' if len(callback_info) > 150 else ''}",
                    "INFO"
                )

                # Fire directly with requests for maximum speed
                response = requests.get(link_url, headers=headers, timeout=5, allow_redirects=True)

                self._log_activity(
                    f"✅ RESPONSE: {link_type_name} {i+1}/{len(links)}{click_id_info} "
                    f"(Status: {response.status_code}, Size: {len(response.content)} bytes)",
                    "SUCCESS"
                )

            except requests.exceptions.Timeout:
                self._log_activity(f"⏰ Timeout firing {link_type_name} {i+1}/{len(links)}", "ERROR")
            except requests.exceptions.RequestException as e:
                self._log_activity(f"❌ Error firing {link_type_name} {i+1}/{len(links)}: {str(e)[:50]}", "ERROR")

            # Short delay between requests
            if i < len(links) - 1:
                time.sleep(delay_between_fires)

        self._log_activity(f"🎯 Finished firing all {len(links)} {link_type_name}s in {len(links) * delay_between_fires:.1f}s!", "SUCCESS")
        return "Firing complete"


    # --- Offer Wall Processor Tab ---
    def _create_offer_wall_tab(self):
        self.offer_wall_tab = QWidget()
        self.tab_widget.addTab(self.offer_wall_tab, "💰 Offer Wall Processor")
        offer_wall_tab_layout = QVBoxLayout(self.offer_wall_tab)

        # Data Input Frame
        data_input_group = QFrame()
        data_input_group.setFrameShape(QFrame.Shape.StyledPanel)
        data_input_layout = QVBoxLayout(data_input_group)

        # Upload JSON
        upload_layout = QHBoxLayout()
        self.ow_upload_btn = QPushButton("📁 Upload JSON Offer File")
        self.ow_upload_btn.clicked.connect(self.ow_upload_json)
        upload_layout.addWidget(self.ow_upload_btn)
        self.ow_json_filename_label = QLabel("No file uploaded.")
        upload_layout.addWidget(self.ow_json_filename_label, 1)
        data_input_layout.addLayout(upload_layout)

        # Fetch by SID
        fetch_sid_layout = QHBoxLayout()
        fetch_sid_layout.addWidget(QLabel(f"🆔 Subscriber ID (SID) for Besitos Wall (partner: {BESITOS_PARTNER_ID}, channel: {BESITOS_CHANNEL}):"))
        self.ow_sid_entry = QLineEdit()
        self.ow_sid_entry.setPlaceholderText("Enter SID")
        fetch_sid_layout.addWidget(self.ow_sid_entry)

        # User Agent Selection
        self.ow_user_agent_combo = QComboBox()
        self.ow_user_agent_combo.addItems(["📱 iOS", "🤖 Android"])
        self.ow_user_agent_combo.setFixedWidth(120)
        fetch_sid_layout.addWidget(self.ow_user_agent_combo)

        # Offer Limit Selection
        fetch_sid_layout.addWidget(QLabel("Limit:"))
        self.ow_limit_combo = QComboBox()
        self.ow_limit_combo.addItems(["50", "100", "200", "500", "1000"])
        self.ow_limit_combo.setCurrentText("500")
        self.ow_limit_combo.setFixedWidth(80)
        fetch_sid_layout.addWidget(self.ow_limit_combo)

        self.ow_fetch_sid_btn = QPushButton("🌐 Fetch Offers by SID")
        self.ow_fetch_sid_btn.clicked.connect(self.ow_fetch_by_sid)
        fetch_sid_layout.addWidget(self.ow_fetch_sid_btn)
        data_input_layout.addLayout(fetch_sid_layout)
        
        info_note = QLabel("Note: Redirect tracing uses Python's 'requests' (handles HTTP/S). Non-HTTP schemes (e.g., market://) are parsed if final, but not actively followed.")
        info_note.setStyleSheet("color: #1a1a2e; background-color: #e94560; padding: 8px; border-radius: 6px; font-style: italic; font-weight: 500;")
        info_note.setWordWrap(True)
        data_input_layout.addWidget(info_note)

        offer_wall_tab_layout.addWidget(data_input_group)
        
        # Offer Display Frame
        offers_display_title = QLabel("Offers & Goals")
        offers_display_title.setStyleSheet("font-size: 12pt; font-weight: bold; margin-top: 15px; color: #eee; background-color: #0f3460; padding: 8px; border-radius: 6px;")
        offer_wall_tab_layout.addWidget(offers_display_title)

        self.ow_offers_scroll_area, self.ow_offers_layout = self._create_scrollable_area()
        offer_wall_tab_layout.addWidget(self.ow_offers_scroll_area)

        self.loaded_offers_data = []

    # --- Besitos Offer Wall Tab ---
    def _create_besitos_wall_tab(self):
        self.besitos_tab = QWidget()
        self.tab_widget.addTab(self.besitos_tab, "🎮 Besitos Wall")
        besitos_tab_layout = QVBoxLayout(self.besitos_tab)

        # Header Section
        header_frame = QFrame()
        header_frame.setFrameShape(QFrame.Shape.StyledPanel)
        header_layout = QVBoxLayout(header_frame)

        # Title
        title_label = QLabel("🎮 <b>BESITOS OFFER WALL</b>")
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                           stop: 0 #e94560, stop: 1 #533483);
                color: #eee;
                padding: 16px;
                border-radius: 8px;
                font-size: 14pt;
                font-weight: bold;
                margin: 8px 0px;
            }
        """)
        header_layout.addWidget(title_label)

        # Fetch Controls
        fetch_controls_layout = QHBoxLayout()

        # SID Input
        fetch_controls_layout.addWidget(QLabel("🆔 Subscriber ID:"))
        self.besitos_sid_entry = QLineEdit()
        self.besitos_sid_entry.setPlaceholderText("Enter SID for Besitos")
        fetch_controls_layout.addWidget(self.besitos_sid_entry)

        # User Agent Selection
        self.besitos_user_agent_combo = QComboBox()
        self.besitos_user_agent_combo.addItems(["📱 iOS", "🤖 Android"])
        self.besitos_user_agent_combo.setFixedWidth(120)
        fetch_controls_layout.addWidget(self.besitos_user_agent_combo)

        # Limit Selection
        fetch_controls_layout.addWidget(QLabel("Limit:"))
        self.besitos_limit_combo = QComboBox()
        self.besitos_limit_combo.addItems(["50", "100", "200", "500", "1000"])
        self.besitos_limit_combo.setCurrentText("500")
        self.besitos_limit_combo.setFixedWidth(80)
        fetch_controls_layout.addWidget(self.besitos_limit_combo)

        # Fetch Button
        self.besitos_fetch_btn = QPushButton("🌐 Fetch Besitos Offers")
        self.besitos_fetch_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                           stop: 0 #e94560, stop: 1 #533483);
                border: 2px solid #e94560;
                padding: 12px 20px;
                min-height: 28px;
                border-radius: 8px;
                color: #eee;
                font-weight: bold;
                font-size: 10pt;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                           stop: 0 #533483, stop: 1 #e94560);
            }
        """)
        self.besitos_fetch_btn.clicked.connect(self.besitos_fetch_offers)
        fetch_controls_layout.addWidget(self.besitos_fetch_btn)

        header_layout.addLayout(fetch_controls_layout)

        # Status Label
        self.besitos_status_label = QLabel("Ready to fetch offers...")
        self.besitos_status_label.setStyleSheet("color: #533483; font-weight: bold; margin: 8px 0px;")
        header_layout.addWidget(self.besitos_status_label)

        besitos_tab_layout.addWidget(header_frame)

        # Offers Display Area
        self.besitos_offers_scroll_area, self.besitos_offers_layout = self._create_scrollable_area()
        besitos_tab_layout.addWidget(self.besitos_offers_scroll_area)

        self.besitos_offers_data = []

    def besitos_fetch_offers(self):
        """Fetch offers from Besitos API and display them in the dedicated tab"""
        sid = self.besitos_sid_entry.text().strip()
        if not sid:
            QMessageBox.warning(self, "Input Required", "Please enter a Subscriber ID (SID).")
            return

        self._log_activity(f"Fetching Besitos offers for SID: {sid}", "INFO")
        self.besitos_status_label.setText(f"<font color='#e94560'><b>Fetching offers for SID:</b> {sid}...</font>")
        self.besitos_fetch_btn.setEnabled(False)

        self.besitos_fetch_worker = GenericWorker(self._besitos_do_fetch_request, sid)
        self.besitos_fetch_worker.result.connect(self._besitos_handle_fetch_result)
        self.besitos_fetch_worker.error.connect(self._besitos_handle_fetch_error)
        self.besitos_fetch_worker.finished.connect(lambda: self.besitos_fetch_btn.setEnabled(True))
        self.besitos_fetch_worker.start()

    def _besitos_do_fetch_request(self, sid):
        """Fetch offers from Besitos API"""
        selected_limit = int(self.besitos_limit_combo.currentText())

        params = {
            'channel': BESITOS_CHANNEL,
            'subscriber_id': sid,
            'partner_id': BESITOS_PARTNER_ID,
            'offset': 0,
            'limit': selected_limit
        }

        # Get selected user agent
        user_agent_selection = self.besitos_user_agent_combo.currentText()
        if "iOS" in user_agent_selection:
            user_agent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
        else:  # Android
            user_agent = 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36'

        headers = {'User-Agent': user_agent}
        api_url = BESITOS_API_BASE_URL

        response = requests.get(api_url, params=params, headers=headers, timeout=20)
        response.raise_for_status()
        data = response.json()

        if not isinstance(data, dict) or "offers" not in data or not isinstance(data["offers"], list):
            if isinstance(data, list):
                return {"offers": data}
            raise ValueError("Invalid API response format.")
        return data

    @Slot(object)
    def _besitos_handle_fetch_result(self, data):
        """Handle successful Besitos API response"""
        sid = self.besitos_sid_entry.text().strip()
        try:
            self.besitos_offers_data = self._parse_offers_from_json(data, f"Besitos_API_SID_{sid}")
            self.besitos_status_label.setText(f"<font color='#533483'><b>✅ Fetched {len(self.besitos_offers_data)} offers for SID:</b> {sid}</font>")
            self._log_activity(f"Successfully fetched {len(self.besitos_offers_data)} Besitos offers for SID: {sid}.", "SUCCESS")
            self._besitos_display_offers()
        except Exception as e:
            self._log_activity(f"Error parsing Besitos data for SID {sid}: {str(e)}", "ERROR")
            self.besitos_status_label.setText(f"<font color='#e94560'><b>❌ Parse Error:</b> {str(e)[:50]}...</font>")

    @Slot(str)
    def _besitos_handle_fetch_error(self, error_message):
        """Handle Besitos API fetch error"""
        sid = self.besitos_sid_entry.text().strip()
        QMessageBox.critical(self, "Besitos API Error", f"Failed to fetch offers: {error_message}")
        self._log_activity(f"Besitos API fetch failed for SID {sid}: {error_message}", "ERROR")
        self.besitos_status_label.setText(f"<font color='#e94560'><b>❌ API Error:</b> {error_message[:100]}</font>")

    def _besitos_display_offers(self):
        """Display Besitos offers in a clean, visual format with app icons and USD payouts"""
        self._clear_layout(self.besitos_offers_layout)

        if not self.besitos_offers_data:
            self.besitos_offers_layout.addWidget(QLabel("No Besitos offers loaded."))
            return

        for offer_data in self.besitos_offers_data:
            offer_card = self._create_besitos_offer_card(offer_data)
            self.besitos_offers_layout.addWidget(offer_card)

    def _create_besitos_offer_card(self, offer_data):
        """Create a visual offer card with app icon, payout, and copyable data"""
        card_frame = QFrame()
        card_frame.setFrameShape(QFrame.Shape.StyledPanel)
        card_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(22, 33, 62, 0.8);
                border: 2px solid #533483;
                border-radius: 12px;
                margin: 8px 4px;
                padding: 16px;
            }
            QFrame:hover {
                border: 2px solid #e94560;
                background-color: rgba(22, 33, 62, 1.0);
            }
        """)

        card_layout = QVBoxLayout(card_frame)
        card_layout.setSpacing(12)

        # Header with App Icon and Basic Info
        header_layout = QHBoxLayout()

        # App Icon
        icon_label = QLabel()
        icon_label.setFixedSize(80, 80)
        icon_label.setStyleSheet("""
            QLabel {
                border: 3px solid #533483;
                border-radius: 12px;
                background-color: #16213e;
                padding: 4px;
            }
        """)

        # Set app icon
        icon_url = offer_data.get('iconUrl', offer_data.get('icon', ''))
        if icon_url:
            icon_label.setText("🎯")
            icon_label.setToolTip(f"Icon URL: {icon_url}")
            # Determine app type for better icon
            offer_name_lower = offer_data.get('name', '').lower()
            if any(word in offer_name_lower for word in ['game', 'play', 'casino', 'slot']):
                icon_label.setText("🎮")
            elif any(word in offer_name_lower for word in ['shop', 'buy', 'store']):
                icon_label.setText("🛒")
            elif any(word in offer_name_lower for word in ['finance', 'bank', 'money']):
                icon_label.setText("💰")
        else:
            icon_label.setText("📱")

        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet(icon_label.styleSheet() + "font-size: 32px;")
        header_layout.addWidget(icon_label)

        # App Info
        info_layout = QVBoxLayout()

        # App Name
        app_name = offer_data.get('name', 'Unknown App')
        name_label = QLabel(f"<b style='font-size: 14pt; color: #eee;'>{app_name}</b>")
        name_label.setWordWrap(True)
        info_layout.addWidget(name_label)

        # App ID and Platform
        app_id = offer_data.get('id', offer_data.get('cid', 'N/A'))
        platform_info = f"ID: {app_id}"
        if 'platform' in offer_data:
            platform_info += f" | Platform: {offer_data['platform']}"

        platform_label = QLabel(f"<span style='color: #533483; font-size: 10pt;'>{platform_info}</span>")
        info_layout.addWidget(platform_label)

        # Total Payout in BIG GREEN USD
        total_payout = offer_data.get('currency', 0)
        if 'payout_amount_micros' in offer_data:
            payout_usd = offer_data['payout_amount_micros'] / 1000000.0
            payout_text = f"${payout_usd:.2f}"
        else:
            # Convert points to approximate USD (assuming 1000 points = $1)
            payout_usd = total_payout / 1000.0
            payout_text = f"~${payout_usd:.2f}"

        payout_label = QLabel(f"<b style='font-size: 18pt; color: #00ff00;'>{payout_text}</b>")
        payout_label.setStyleSheet("background-color: rgba(0, 255, 0, 0.1); padding: 8px; border-radius: 6px; border: 2px solid #00ff00;")
        info_layout.addWidget(payout_label)

        header_layout.addLayout(info_layout)
        header_layout.addStretch()

        # Quick Action Buttons
        actions_layout = QVBoxLayout()

        # Copy App Link Button
        app_url = offer_data.get('url', offer_data.get('click_url', ''))
        if app_url:
            copy_link_btn = QPushButton("📋 Copy App Link")
            copy_link_btn.setStyleSheet("""
                QPushButton {
                    background-color: #533483;
                    border: 1px solid #0f3460;
                    padding: 8px 16px;
                    border-radius: 6px;
                    color: #eee;
                    font-weight: bold;
                }
                QPushButton:hover { background-color: #0f3460; }
            """)
            copy_link_btn.clicked.connect(lambda: self.copy_to_clipboard(app_url, f"{app_name} link"))
            actions_layout.addWidget(copy_link_btn)

        # Copy Icon URL Button
        if icon_url:
            copy_icon_btn = QPushButton("🖼️ Copy Icon URL")
            copy_icon_btn.setStyleSheet("""
                QPushButton {
                    background-color: #0f3460;
                    border: 1px solid #533483;
                    padding: 8px 16px;
                    border-radius: 6px;
                    color: #eee;
                    font-weight: bold;
                }
                QPushButton:hover { background-color: #533483; }
            """)
            copy_icon_btn.clicked.connect(lambda: self.copy_to_clipboard(icon_url, f"{app_name} icon URL"))
            actions_layout.addWidget(copy_icon_btn)

        header_layout.addLayout(actions_layout)
        card_layout.addLayout(header_layout)

        # Goals/Events Section
        goals = offer_data.get('goals', [])
        if goals:
            goals_header = QLabel(f"🎯 <b>GOALS & EVENTS ({len(goals)})</b>")
            goals_header.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #533483, stop: 1 #0f3460);
                    color: #eee;
                    padding: 8px;
                    border-radius: 6px;
                    font-size: 11pt;
                    font-weight: bold;
                }
            """)
            card_layout.addWidget(goals_header)

            # Goals Grid
            goals_grid_widget = QWidget()
            goals_grid_layout = QVBoxLayout(goals_grid_widget)

            for i, goal in enumerate(goals):
                goal_widget = self._create_besitos_goal_widget(goal, i, app_name)
                goals_grid_layout.addWidget(goal_widget)

            card_layout.addWidget(goals_grid_widget)

        return card_frame

    def _create_besitos_goal_widget(self, goal, index, app_name):
        """Create a goal widget with copyable data for Besitos offers"""
        goal_frame = QFrame()
        goal_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(15, 52, 96, 0.4);
                border: 1px solid #0f3460;
                border-radius: 8px;
                margin: 4px 0px;
                padding: 12px;
            }
        """)

        goal_layout = QVBoxLayout(goal_frame)
        goal_layout.setSpacing(8)

        # Goal Header
        goal_name = goal.get('name', goal.get('text', f'Goal {index + 1}'))
        goal_id = goal.get('goal_id', goal.get('id', 'N/A'))

        header_layout = QHBoxLayout()

        # Goal Title and ID
        goal_title = QLabel(f"<b style='color: #eee; font-size: 11pt;'>#{index + 1}: {goal_name}</b>")
        goal_title.setWordWrap(True)
        header_layout.addWidget(goal_title)

        header_layout.addStretch()

        # Goal ID Badge
        goal_id_label = QLabel(f"<b>ID: {goal_id}</b>")
        goal_id_label.setStyleSheet("""
            QLabel {
                background-color: #533483;
                color: #eee;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 9pt;
                font-weight: bold;
            }
        """)
        header_layout.addWidget(goal_id_label)

        goal_layout.addLayout(header_layout)

        # Goal Payout
        goal_payout = goal.get('currency', 0)
        if 'payout_amount_micros' in goal:
            payout_usd = goal['payout_amount_micros'] / 1000000.0
            payout_text = f"${payout_usd:.2f}"
        else:
            payout_usd = goal_payout / 1000.0
            payout_text = f"~${payout_usd:.2f}" if goal_payout > 0 else "Install Only"

        payout_label = QLabel(f"<b style='color: #00ff00; font-size: 12pt;'>{payout_text}</b>")
        payout_label.setStyleSheet("background-color: rgba(0, 255, 0, 0.1); padding: 6px; border-radius: 4px; border: 1px solid #00ff00;")
        goal_layout.addWidget(payout_label)

        # Goal Metadata (if available)
        if 'metadata' in goal:
            metadata = goal['metadata']
            metadata_info = []

            if metadata.get('maxTime') and metadata.get('maxTime') != 'null':
                max_time = metadata['maxTime']
                time_metric = metadata.get('maxTimeMetric', 'days')
                metadata_info.append(f"⏰ Complete within {max_time} {time_metric}")

            if metadata.get('eventCategory'):
                metadata_info.append(f"📂 Category: {metadata['eventCategory']}")

            if metadata_info:
                metadata_label = QLabel(" | ".join(metadata_info))
                metadata_label.setStyleSheet("color: #533483; font-size: 9pt; font-style: italic;")
                metadata_label.setWordWrap(True)
                goal_layout.addWidget(metadata_label)

        # Copyable Data Section
        copyable_data_layout = QHBoxLayout()

        # Copy Goal ID Button
        copy_id_btn = QPushButton(f"📋 Copy ID")
        copy_id_btn.setFixedWidth(80)
        copy_id_btn.setStyleSheet("""
            QPushButton {
                background-color: #0f3460;
                border: 1px solid #533483;
                padding: 4px 8px;
                border-radius: 4px;
                color: #eee;
                font-size: 8pt;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #533483; }
        """)
        copy_id_btn.clicked.connect(lambda: self.copy_to_clipboard(str(goal_id), f"{app_name} Goal {index + 1} ID"))
        copyable_data_layout.addWidget(copy_id_btn)

        # Copy Goal Name Button
        copy_name_btn = QPushButton(f"📝 Copy Name")
        copy_name_btn.setFixedWidth(90)
        copy_name_btn.setStyleSheet("""
            QPushButton {
                background-color: #533483;
                border: 1px solid #0f3460;
                padding: 4px 8px;
                border-radius: 4px;
                color: #eee;
                font-size: 8pt;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #0f3460; }
        """)
        copy_name_btn.clicked.connect(lambda: self.copy_to_clipboard(goal_name, f"{app_name} Goal {index + 1} name"))
        copyable_data_layout.addWidget(copy_name_btn)

        # Copy Payout Button
        copy_payout_btn = QPushButton(f"💰 Copy Payout")
        copy_payout_btn.setFixedWidth(100)
        copy_payout_btn.setStyleSheet("""
            QPushButton {
                background-color: #e94560;
                border: 1px solid #533483;
                padding: 4px 8px;
                border-radius: 4px;
                color: #eee;
                font-size: 8pt;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #533483; }
        """)
        copy_payout_btn.clicked.connect(lambda: self.copy_to_clipboard(str(goal_payout), f"{app_name} Goal {index + 1} payout"))
        copyable_data_layout.addWidget(copy_payout_btn)

        # Copy All Goal Data Button
        copy_all_btn = QPushButton(f"📊 Copy All Data")
        copy_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #16213e;
                border: 1px solid #533483;
                padding: 4px 8px;
                border-radius: 4px;
                color: #eee;
                font-size: 8pt;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #533483; }
        """)

        # Prepare all goal data for copying
        goal_data_text = f"Goal Name: {goal_name}\nGoal ID: {goal_id}\nPayout: {goal_payout} points ({payout_text})"
        if 'metadata' in goal:
            goal_data_text += f"\nMetadata: {json.dumps(goal['metadata'], indent=2)}"

        copy_all_btn.clicked.connect(lambda: self.copy_to_clipboard(goal_data_text, f"{app_name} Goal {index + 1} complete data"))
        copyable_data_layout.addWidget(copy_all_btn)

        # Show Raw Data Button
        show_data_btn = QPushButton(f"🔍 Show Raw Data")
        show_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #e94560;
                border: 1px solid #533483;
                padding: 4px 8px;
                border-radius: 4px;
                color: #eee;
                font-size: 8pt;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #533483; }
        """)
        show_data_btn.clicked.connect(lambda: self._show_goal_raw_data(goal, app_name, index))
        copyable_data_layout.addWidget(show_data_btn)

        copyable_data_layout.addStretch()
        goal_layout.addLayout(copyable_data_layout)

        return goal_frame

    def _show_goal_raw_data(self, goal, app_name, index):
        """Show a detailed popup with all the raw goal data and what would be sent"""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"Raw Data: {app_name} - Goal {index + 1}")
        dialog.setMinimumSize(600, 400)
        dialog.setStyleSheet("""
            QDialog {
                background-color: #1a1a2e;
                color: #eee;
            }
            QTextEdit {
                background-color: #16213e;
                border: 2px solid #533483;
                color: #eee;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 9pt;
            }
        """)

        layout = QVBoxLayout(dialog)

        # Title
        title_label = QLabel(f"<b style='font-size: 12pt;'>📊 Raw Goal Data & Callback Preview</b>")
        title_label.setStyleSheet("color: #eee; padding: 8px; background-color: #533483; border-radius: 4px;")
        layout.addWidget(title_label)

        # Raw JSON data
        raw_data_text = QTextEdit()
        raw_data_text.setReadOnly(True)

        # Format the goal data nicely
        goal_json = json.dumps(goal, indent=2, ensure_ascii=False)

        # Add callback preview if this were to be sent
        goal_id = goal.get('goal_id', goal.get('id', 'N/A'))
        goal_name = goal.get('name', goal.get('text', 'N/A'))
        goal_payout = goal.get('currency', 0)

        callback_preview = f"""
=== CALLBACK DATA THAT WOULD BE SENT ===
Goal ID: {goal_id}
Goal Name: {goal_name}
Payout: {goal_payout} points
Transaction ID: [Would be generated when TxID is fetched]
Click ID: [Unique UUID would be generated]

Example Callback URL Structure:
https://app.adjust.com/10y4cdbj?click_callback=https://your-callback-server.com/callback?kashkick_event=1&goal_id={goal_id}&transaction_id=[TX_ID]&click_id=[CLICK_ID]&goal_name={goal_name[:20]}&payout={goal_payout}

=== RAW GOAL JSON DATA ===
{goal_json}
"""

        raw_data_text.setPlainText(callback_preview)
        layout.addWidget(raw_data_text)

        # Buttons
        button_layout = QHBoxLayout()

        copy_btn = QPushButton("📋 Copy All Data")
        copy_btn.clicked.connect(lambda: self.copy_to_clipboard(callback_preview, f"{app_name} Goal {index + 1} raw data"))
        button_layout.addWidget(copy_btn)

        close_btn = QPushButton("❌ Close")
        close_btn.clicked.connect(dialog.close)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        dialog.exec()

    def _parse_offers_from_json(self, data, filename):
        """
        Robust JSON parser that handles multiple offer wall formats:
        - Ayet: {"offers_array": [...]}
        - Besitos: {"offers": [...]}
        - Gravypass: {"offers": [...]}
        - Monlix: [...]  (direct array)
        - And other variations
        """
        offers = []

        try:
            # Handle direct array format (like Monlix)
            if isinstance(data, list):
                offers = data
                self._log_activity(f"Detected direct array format with {len(offers)} offers.", "INFO")

            # Handle object formats
            elif isinstance(data, dict):
                # Try different possible keys for offers
                possible_keys = ['offers', 'offers_array', 'data', 'results', 'items']

                for key in possible_keys:
                    if key in data and isinstance(data[key], list):
                        offers = data[key]
                        self._log_activity(f"Detected '{key}' format with {len(offers)} offers.", "INFO")
                        break

                # If no offers found in standard keys, check if the object itself contains offer-like data
                if not offers and self._looks_like_offer(data):
                    offers = [data]  # Single offer wrapped in array
                    self._log_activity("Detected single offer format.", "INFO")

                # If still no offers, try to find nested structures
                if not offers:
                    for key, value in data.items():
                        if isinstance(value, list) and value and self._looks_like_offer(value[0]):
                            offers = value
                            self._log_activity(f"Found offers in nested key '{key}' with {len(offers)} offers.", "INFO")
                            break

            if not offers:
                raise ValueError("No valid offers found in JSON. Supported formats: direct array, {offers: []}, {offers_array: []}, etc.")

            # Normalize offer data to ensure consistent structure
            normalized_offers = []
            for offer in offers:
                if isinstance(offer, dict):
                    normalized_offers.append(self._normalize_offer_data(offer))

            return normalized_offers

        except Exception as e:
            raise ValueError(f"Error parsing offers: {str(e)}")

    def _looks_like_offer(self, obj):
        """Check if an object looks like an offer by checking for common offer fields"""
        if not isinstance(obj, dict):
            return False

        # Common offer fields across different platforms
        offer_indicators = ['name', 'title', 'id', 'offer_id', 'url', 'click_url', 'tracking_link', 'payout', 'amount', 'goals']
        return any(key in obj for key in offer_indicators)

    def _normalize_offer_data(self, offer):
        """Normalize offer data to have consistent field names"""
        normalized = offer.copy()

        # Normalize name/title
        if 'title' in offer and 'name' not in offer:
            normalized['name'] = offer['title']
        elif 'name' not in offer and 'title' not in offer:
            normalized['name'] = f"Offer {offer.get('id', offer.get('offer_id', 'Unknown'))}"

        # Normalize ID
        if 'offer_id' in offer and 'id' not in offer:
            normalized['id'] = offer['offer_id']
        elif 'id' not in offer and 'offer_id' not in offer:
            normalized['id'] = 'unknown'

        # Normalize tracking URL
        url_fields = ['click_url', 'tracking_link', 'url', 'link']
        for field in url_fields:
            if field in offer and 'click_url' not in normalized:
                normalized['click_url'] = offer[field]
                break

        # Normalize icon URL
        icon_fields = ['icon', 'iconUrl', 'icon_url', 'image', 'imageUrl', 'image_url', 'square_image']
        for field in icon_fields:
            if field in offer and 'icon' not in normalized:
                normalized['icon'] = offer[field]
                break

        return normalized

    def ow_upload_json(self):
        filepath, _ = QFileDialog.getOpenFileName(self, "Select JSON Offer File", "", "JSON files (*.json);;All files (*)")
        if not filepath: return

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Use the robust parser
            self.loaded_offers_data = self._parse_offers_from_json(data, os.path.basename(filepath))

            self.ow_json_filename_label.setText(f"<font color='#eee'><b>Loaded:</b> {os.path.basename(filepath)} ({len(self.loaded_offers_data)} offers)</font>")
            self._log_activity(f"Successfully loaded {len(self.loaded_offers_data)} offers from '{os.path.basename(filepath)}'.", "SUCCESS")
            self._ow_display_offers()

        except json.JSONDecodeError as e:
            QMessageBox.critical(self, "JSON Error", f"Failed to parse JSON file: {str(e)}")
            self._log_activity(f"Failed to parse JSON file '{os.path.basename(filepath)}': {str(e)}", "ERROR")
            self.ow_json_filename_label.setText("<font color='#eee'><b>JSON Parse Error.</b></font>")
        except ValueError as e:
            QMessageBox.critical(self, "Format Error", str(e))
            self._log_activity(f"Format error in '{os.path.basename(filepath)}': {str(e)}", "ERROR")
            self.ow_json_filename_label.setText("<font color='#eee'><b>Format Error.</b></font>")
        except Exception as e:
            QMessageBox.critical(self, "File Error", f"Error reading file: {e}")
            self._log_activity(f"Error reading file '{os.path.basename(filepath)}': {e}", "ERROR")
            self.ow_json_filename_label.setText(f"<font color='#eee'><b>File Read Error:</b> {e}</font>")

    def ow_fetch_by_sid(self):
        sid = self.ow_sid_entry.text().strip()
        if not sid:
            QMessageBox.warning(self, "Input Required", "Please enter a Subscriber ID (SID).")
            return

        self._log_activity(f"Attempting to fetch Besitos offers for SID: {sid}", "INFO")
        self.ow_json_filename_label.setText(f"<font color='#eee'><b>Fetching for SID:</b> {sid}...</font>")
        self.ow_fetch_sid_btn.setEnabled(False)
        self.ow_upload_btn.setEnabled(False)

        self.fetch_sid_worker = GenericWorker(self._ow_do_fetch_by_sid_request, sid)
        self.fetch_sid_worker.result.connect(self._ow_handle_fetch_sid_result)
        self.fetch_sid_worker.error.connect(self._ow_handle_fetch_sid_error)
        self.fetch_sid_worker.finished.connect(lambda: [
            self.ow_fetch_sid_btn.setEnabled(True),
            self.ow_upload_btn.setEnabled(True)
        ])
        self.fetch_sid_worker.start()

    def _ow_do_fetch_by_sid_request(self, sid):
        # This function runs in the worker thread
        # Get selected limit from combo box
        selected_limit = int(self.ow_limit_combo.currentText())

        params = {
            'channel': BESITOS_CHANNEL,
            'subscriber_id': sid,
            'partner_id': BESITOS_PARTNER_ID,
            'offset': 0,
            'limit': selected_limit  # Use selected limit
        }

        # Get selected user agent from combo box
        user_agent_selection = self.ow_user_agent_combo.currentText()
        if "iOS" in user_agent_selection:
            user_agent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
        else:  # Android
            user_agent = 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36'

        headers = {'User-Agent': user_agent}
        api_url = BESITOS_API_BASE_URL

        response = requests.get(api_url, params=params, headers=headers, timeout=20)
        response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
        data = response.json()

        if not isinstance(data, dict) or "offers" not in data or not isinstance(data["offers"], list):
            # It seems Besitos might return a list directly if offers exist, or an object with "offers":[] if none
            if isinstance(data, list): # If API returns a list of offers directly
                return {"offers": data} # Wrap it to match expected structure
            raise ValueError("Invalid API response format. Expected an object with 'offers' array or a direct list of offers.")
        return data


    @Slot(object)
    def _ow_handle_fetch_sid_result(self, data):
        sid = self.ow_sid_entry.text().strip() # Get SID again for logging/display
        try:
            # Use the robust parser for API data too
            self.loaded_offers_data = self._parse_offers_from_json(data, f"API_SID_{sid}")
            self.ow_json_filename_label.setText(f"<font color='#eee'><b>Fetched</b> {len(self.loaded_offers_data)} offers for SID: {sid}</font>")
            self._log_activity(f"Successfully fetched {len(self.loaded_offers_data)} offers for SID: {sid}.", "SUCCESS")
            self._ow_display_offers()
        except Exception as e:
            self._log_activity(f"Error parsing fetched data for SID {sid}: {str(e)}", "ERROR")
            self.ow_json_filename_label.setText(f"<font color='#eee'><b>Parse Error:</b> {str(e)[:50]}...</font>")

    @Slot(str)
    def _ow_handle_fetch_sid_error(self, error_message):
        sid = self.ow_sid_entry.text().strip()
        QMessageBox.critical(self, "API Fetch Error", f"Failed to fetch offers: {error_message}")
        self._log_activity(f"API fetch failed for SID {sid}: {error_message}", "ERROR")
        self.ow_json_filename_label.setText(f"<font color='#eee'><b>API Fetch Error:</b> {error_message[:100]}</font>")

    def _ow_display_offers(self):
        self._clear_layout(self.ow_offers_layout)
        if not self.loaded_offers_data:
            self.ow_offers_layout.addWidget(QLabel("No offers loaded or found."))
            return

        for offer_data in self.loaded_offers_data:
            offer_frame_container = QFrame() # Container to hold the button and the details frame
            offer_frame_container.setFrameShape(QFrame.Shape.StyledPanel)
            offer_main_layout = QVBoxLayout(offer_frame_container)

            # Enhanced Offer Header with Icon
            offer_header_layout = QHBoxLayout()

            # Add offer icon if available
            icon_url = offer_data.get('icon', offer_data.get('iconUrl', offer_data.get('icon_url', offer_data.get('image_url', offer_data.get('square_image', '')))))
            icon_label = QLabel()
            icon_label.setFixedSize(56, 56)
            icon_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #533483;
                    border-radius: 8px;
                    background-color: #16213e;
                    padding: 4px;
                    font-size: 24px;
                }
            """)

            # Set appropriate icon based on offer type or use default
            if icon_url:
                icon_label.setText("🎯")  # Default app icon
                icon_label.setToolTip(f"Icon URL: {icon_url}")
                # Try to determine app type from name for better icons
                offer_name_lower = offer_data.get('name', '').lower()
                if any(word in offer_name_lower for word in ['game', 'play', 'casino', 'slot']):
                    icon_label.setText("🎮")
                elif any(word in offer_name_lower for word in ['shop', 'buy', 'store', 'purchase']):
                    icon_label.setText("🛒")
                elif any(word in offer_name_lower for word in ['finance', 'bank', 'money', 'loan', 'credit']):
                    icon_label.setText("💰")
                elif any(word in offer_name_lower for word in ['food', 'delivery', 'restaurant', 'eat']):
                    icon_label.setText("🍔")
                elif any(word in offer_name_lower for word in ['fitness', 'health', 'workout', 'exercise']):
                    icon_label.setText("💪")
                elif any(word in offer_name_lower for word in ['dating', 'match', 'meet']):
                    icon_label.setText("💕")
                elif any(word in offer_name_lower for word in ['music', 'audio', 'sound', 'radio']):
                    icon_label.setText("🎵")
                elif any(word in offer_name_lower for word in ['video', 'stream', 'watch', 'tv']):
                    icon_label.setText("📺")
            else:
                icon_label.setText("📱")  # Default mobile app icon

            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            offer_header_layout.addWidget(icon_label)

            # Offer info section
            offer_info_layout = QVBoxLayout()
            offer_name = offer_data.get('name', offer_data.get('title', 'Unnamed Offer'))
            offer_id = offer_data.get('id', offer_data.get('offer_id', offer_data.get('cid', 'N/A')))

            # Main title with enhanced styling
            title_label = QLabel(f"<b style='font-size: 12pt; color: #eee;'>{offer_name}</b>")
            title_label.setWordWrap(True)
            offer_info_layout.addWidget(title_label)

            # Subtitle with ID and additional info
            subtitle_parts = [f"ID: {offer_id}"]
            if 'platform' in offer_data:
                subtitle_parts.append(f"Platform: {offer_data['platform']}")
            if 'payout' in offer_data:
                subtitle_parts.append(f"Payout: {offer_data['payout']}")
            if 'amount' in offer_data:
                subtitle_parts.append(f"Amount: {offer_data['amount']}")
            if 'currency' in offer_data and offer_data['currency'] > 0:
                subtitle_parts.append(f"Total Payout: {offer_data['currency']} points")
            if 'payout_amount_micros' in offer_data:
                payout_dollars = offer_data['payout_amount_micros'] / 1000000.0
                subtitle_parts.append(f"Payout: ${payout_dollars:.2f}")

            subtitle_label = QLabel(f"<span style='color: #533483; font-size: 9pt;'>{' | '.join(subtitle_parts)}</span>")
            subtitle_label.setWordWrap(True)
            offer_info_layout.addWidget(subtitle_label)

            # Add icon URL as clickable link if present
            if icon_url:
                icon_link_label = QLabel(f"<a href='{icon_url}' style='color: #0f3460; font-size: 8pt;'>View Icon</a>")
                icon_link_label.setOpenExternalLinks(True)
                offer_info_layout.addWidget(icon_link_label)

            offer_header_layout.addLayout(offer_info_layout)
            offer_header_layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
            offer_main_layout.addLayout(offer_header_layout)

            # Show goals preview BEFORE fetching TxID
            goals = offer_data.get('goals', [])
            if goals:
                goals_preview_label = QLabel(f"<b style='color: #533483;'>📋 Available Goals ({len(goals)}):</b>")
                offer_main_layout.addWidget(goals_preview_label)

                # Show first few goals as preview
                preview_goals = goals[:3]  # Show first 3 goals
                for i, goal in enumerate(preview_goals):
                    goal_name = goal.get('name', goal.get('text', f'Goal {i+1}'))
                    goal_currency = goal.get('currency', 0)
                    goal_id = goal.get('id', goal.get('goal_id', 'N/A'))

                    if goal_currency > 0:
                        goal_preview = QLabel(f"  • {goal_name} (ID: {goal_id}) - {goal_currency} points")
                    else:
                        goal_preview = QLabel(f"  • {goal_name} (ID: {goal_id}) - Install only")

                    goal_preview.setStyleSheet("color: #eee; font-size: 9pt; margin-left: 10px;")
                    goal_preview.setWordWrap(True)
                    offer_main_layout.addWidget(goal_preview)

                if len(goals) > 3:
                    more_goals_label = QLabel(f"  ... and {len(goals) - 3} more goals")
                    more_goals_label.setStyleSheet("color: #533483; font-size: 8pt; font-style: italic; margin-left: 10px;")
                    offer_main_layout.addWidget(more_goals_label)

            # Button to fetch TxID - this button will be replaced by details once fetched
            fetch_tx_button = QPushButton("🔍 Fetch Transaction ID & Generate Event Callbacks")
            fetch_tx_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #e94560, stop: 1 #533483);
                    border: 2px solid #e94560;
                    padding: 12px 20px;
                    min-height: 28px;
                    border-radius: 8px;
                    color: #eee;
                    font-weight: bold;
                    font-size: 10pt;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #533483, stop: 1 #e94560);
                }
            """)
            offer_main_layout.addWidget(fetch_tx_button)

            # Placeholder for details to be populated after fetching TxID
            details_widget = QWidget() # This widget will hold TxID info and goals
            details_layout = QVBoxLayout(details_widget)
            details_widget.setVisible(False) # Initially hidden
            offer_main_layout.addWidget(details_widget)

            fetch_tx_button.clicked.connect(
                lambda checked=False, data=offer_data, btn=fetch_tx_button, widget=details_widget, layout=details_layout:
                self.ow_trigger_fetch_transaction_id(data, btn, widget, layout)
            )
            self.ow_offers_layout.addWidget(offer_frame_container)

    def ow_trigger_fetch_transaction_id(self, offer_data, button_to_hide, details_widget, details_layout):
        initial_url = offer_data.get('click_url', offer_data.get('tracking_link')) # Common keys
        if not initial_url:
            QMessageBox.warning(self, "Missing URL", f"Offer '{offer_data.get('name', 'N/A')}' has no tracking link.")
            self._log_activity(f"Offer '{offer_data.get('name', 'N/A')}' has no tracking link.", "ERROR")
            return

        button_to_hide.setText("Processing... Following redirects...")
        button_to_hide.setEnabled(False)
        
        self._log_activity(f"Fetching TxID for '{offer_data.get('name', 'N/A')}': {initial_url}", "INFO")

        self.txid_worker = GenericWorker(self._ow_do_fetch_txid_request, initial_url)
        self.txid_worker.result.connect(lambda result_data: self._ow_handle_txid_result(
            result_data, offer_data, button_to_hide, details_widget, details_layout
        ))
        self.txid_worker.error.connect(lambda error_msg: self._ow_handle_txid_error(
            error_msg, offer_data, button_to_hide, details_layout
        ))
        # No finished signal needed to re-enable button as it's replaced/hidden
        self.txid_worker.start()

    def _ow_do_fetch_txid_request(self, initial_url):
        import uuid

        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}

        # Generate unique click ID for this event
        click_id = str(uuid.uuid4())

        # Capture redirect chain manually to analyze intermediate URLs
        redirect_chain = []
        current_url = initial_url
        max_redirects = 10
        redirect_count = 0

        try:
            while redirect_count < max_redirects:
                response = requests.get(current_url, headers=headers, allow_redirects=False, timeout=25)
                redirect_chain.append({
                    'url': current_url,
                    'status_code': response.status_code,
                    'headers': dict(response.headers)
                })

                # Check if this is a redirect
                if response.status_code in [301, 302, 303, 307, 308]:
                    location = response.headers.get('Location')
                    if location:
                        # Handle relative URLs
                        if location.startswith('/'):
                            parsed_current = urlparse(current_url)
                            current_url = f"{parsed_current.scheme}://{parsed_current.netloc}{location}"
                        elif not location.startswith(('http://', 'https://')):
                            # Relative to current path
                            parsed_current = urlparse(current_url)
                            base_path = '/'.join(parsed_current.path.split('/')[:-1])
                            current_url = f"{parsed_current.scheme}://{parsed_current.netloc}{base_path}/{location}"
                        else:
                            current_url = location
                        redirect_count += 1
                    else:
                        break
                else:
                    # Final destination reached
                    break

        except requests.exceptions.Timeout:
            raise TimeoutError(f"Request timed out after 25s for URL: {initial_url}")
        except requests.exceptions.RequestException as e:
            raise ConnectionError(f"Network error for URL {initial_url}: {e}")

        final_url = current_url

        # Now analyze the entire redirect chain for TxID
        tx_id = None
        mmp_type = "Unknown"
        found_in_url = None

        # Check each URL in the redirect chain with enhanced MMP detection
        for redirect_info in redirect_chain:
            url = redirect_info['url']
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)

            # Enhanced Adjust detection
            if any(domain in parsed_url.netloc.lower() for domain in ["adjust.com", "adj.st", "adjust.io"]):
                mmp_type = "Adjust"

                # Check install_callback parameter
                install_cb = query_params.get('install_callback', [None])[0]
                if install_cb:
                    try:
                        decoded_cb = unquote_plus(install_cb)
                        cb_params = parse_qs(urlparse(decoded_cb).query)
                        tx_id = cb_params.get('transaction_id', [None])[0] or cb_params.get('click_id', [None])[0]
                        if tx_id:
                            found_in_url = url
                            break
                    except:
                        pass

                # Check event_callback parameters
                if not tx_id:
                    for k, v in query_params.items():
                        if k.startswith('event_callback_'):
                            try:
                                decoded_cb = unquote_plus(v[0])
                                cb_params = parse_qs(urlparse(decoded_cb).query)
                                tx_id = cb_params.get('transaction_id', [None])[0] or cb_params.get('click_id', [None])[0]
                                if tx_id:
                                    found_in_url = url
                                    break
                            except:
                                continue

                # Check direct parameters
                if not tx_id:
                    tx_id = (query_params.get('adj_t', [None])[0] or  # Adjust tracker token
                            query_params.get('click_id', [None])[0] or
                            query_params.get('transaction_id', [None])[0] or
                            query_params.get('adid', [None])[0])  # Adjust device ID
                    if tx_id:
                        found_in_url = url
                        break

            # Enhanced AppsFlyer detection
            elif any(domain in parsed_url.netloc.lower() for domain in ["appsflyer.com", "onelink.me", "app.appsflyer.com"]):
                mmp_type = "AppsFlyer"

                # AppsFlyer specific parameters
                tx_id = (query_params.get('af_click_lookback', [None])[0] or
                        query_params.get('clickid', [None])[0] or
                        query_params.get('af_siteid', [None])[0] or
                        query_params.get('af_sub1', [None])[0] or
                        query_params.get('af_sub2', [None])[0] or
                        query_params.get('af_sub3', [None])[0] or
                        query_params.get('af_sub4', [None])[0] or
                        query_params.get('af_sub5', [None])[0] or
                        query_params.get('pid', [None])[0] or  # Partner ID
                        query_params.get('c', [None])[0])     # Campaign
                if tx_id:
                    found_in_url = url
                    break

            # Branch.io detection
            elif any(domain in parsed_url.netloc.lower() for domain in ["branch.io", "bnc.lt", "app.link"]):
                mmp_type = "Branch"
                tx_id = (query_params.get('~click_id', [None])[0] or
                        query_params.get('~id', [None])[0] or
                        query_params.get('click_id', [None])[0])
                if tx_id:
                    found_in_url = url
                    break

            # Kochava detection
            elif "kochava.com" in parsed_url.netloc.lower() or "ko-chava.com" in parsed_url.netloc.lower():
                mmp_type = "Kochava"
                tx_id = (query_params.get('click_id', [None])[0] or
                        query_params.get('network_id', [None])[0] or
                        query_params.get('site_id', [None])[0])
                if tx_id:
                    found_in_url = url
                    break

            # Singular detection
            elif "singular.net" in parsed_url.netloc.lower():
                mmp_type = "Singular"
                tx_id = (query_params.get('click_id', [None])[0] or
                        query_params.get('sng_click_id', [None])[0])
                if tx_id:
                    found_in_url = url
                    break

            # Tune/HasOffers detection
            elif any(domain in parsed_url.netloc.lower() for domain in ["tune.com", "hasoffers.com", "mobileapptracking.com"]):
                mmp_type = "Tune/HasOffers"
                tx_id = (query_params.get('transaction_id', [None])[0] or
                        query_params.get('click_id', [None])[0] or
                        query_params.get('mat_click_id', [None])[0])
                if tx_id:
                    found_in_url = url
                    break

            # Enhanced referrer parameter detection
            elif parsed_url.scheme in ["https", "http"]:
                referrer_str = query_params.get('referrer', [None])[0]
                if referrer_str:
                    try:
                        decoded_referrer = unquote_plus(referrer_str)
                        ref_params = parse_qs(decoded_referrer)

                        # AppsFlyer referrer parameters
                        af_tranid = ref_params.get('af_tranid', [None])[0]
                        if af_tranid:
                            tx_id = af_tranid
                            mmp_type = "AppsFlyer (af_tranid from referrer)"
                            found_in_url = url
                            break

                        # Other AppsFlyer referrer parameters
                        af_clickid = (ref_params.get('clickid', [None])[0] or
                                     ref_params.get('af_click_lookback', [None])[0] or
                                     ref_params.get('af_siteid', [None])[0])
                        if af_clickid:
                            tx_id = af_clickid
                            mmp_type = "AppsFlyer (from referrer)"
                            found_in_url = url
                            break

                        # Adjust referrer parameters
                        adj_params = (ref_params.get('adj_t', [None])[0] or
                                     ref_params.get('transaction_id', [None])[0] or
                                     ref_params.get('click_id', [None])[0])
                        if adj_params:
                            tx_id = adj_params
                            mmp_type = "Adjust (from referrer)"
                            found_in_url = url
                            break
                    except:
                        pass

                # Comprehensive generic parameter detection
                tx_id = (query_params.get('clickid', [None])[0] or
                        query_params.get('click_id', [None])[0] or
                        query_params.get('transaction_id', [None])[0] or
                        query_params.get('publisher_click_id', [None])[0] or
                        query_params.get('user_id', [None])[0] or
                        query_params.get('tracking_id', [None])[0] or
                        query_params.get('sub_id', [None])[0] or
                        query_params.get('sub1', [None])[0] or
                        query_params.get('sub2', [None])[0] or
                        query_params.get('sub3', [None])[0] or
                        query_params.get('sub4', [None])[0] or
                        query_params.get('sub5', [None])[0] or
                        query_params.get('aff_id', [None])[0] or
                        query_params.get('affiliate_id', [None])[0] or
                        query_params.get('campaign_id', [None])[0] or
                        query_params.get('source_id', [None])[0] or
                        query_params.get('external_id', [None])[0])

                if tx_id:
                    # Try to identify the network based on domain patterns
                    domain = parsed_url.netloc.lower()
                    if any(pattern in domain for pattern in ["ltv", "ltvplus", "ltv-mob"]):
                        mmp_type = "LTV Plus"
                    elif any(pattern in domain for pattern in ["impact", "impactradius"]):
                        mmp_type = "Impact Radius"
                    elif any(pattern in domain for pattern in ["cj", "commission-junction"]):
                        mmp_type = "Commission Junction"
                    elif any(pattern in domain for pattern in ["shareasale"]):
                        mmp_type = "ShareASale"
                    elif any(pattern in domain for pattern in ["clickbank"]):
                        mmp_type = "ClickBank"
                    else:
                        mmp_type = f"Generic ({domain})"
                    found_in_url = url
                    break

        # Enhanced final URL detection if no TxID found in redirect chain
        if not tx_id:
            final_url_parsed = urlparse(final_url)
            final_query_params = parse_qs(final_url_parsed.query)

            # Handle market:// scheme (typically AppsFlyer)
            if final_url_parsed.scheme == "market":
                mmp_type = "AppsFlyer (market scheme)"
                referrer_str = final_query_params.get('referrer', [None])[0]
                if referrer_str:
                    try:
                        ref_params = parse_qs(referrer_str)
                        tx_id = (ref_params.get('af_tranid', [None])[0] or
                                ref_params.get('clickid', [None])[0] or
                                ref_params.get('af_click_lookback', [None])[0] or
                                ref_params.get('af_siteid', [None])[0])
                        if tx_id:
                            found_in_url = final_url
                    except:
                        pass

            # Handle intent:// scheme (Android deep links)
            elif final_url_parsed.scheme == "intent":
                mmp_type = "Android Intent (deep link)"
                # Extract parameters from intent URL
                intent_params = final_url_parsed.fragment or final_url_parsed.query
                if intent_params:
                    try:
                        params = parse_qs(intent_params)
                        tx_id = (params.get('referrer', [None])[0] or
                                params.get('click_id', [None])[0] or
                                params.get('transaction_id', [None])[0])
                        if tx_id:
                            found_in_url = final_url
                    except:
                        pass

            # Handle regular HTTP/HTTPS URLs
            else:
                # Comprehensive parameter search for final URL
                tx_id = (final_query_params.get('clickid', [None])[0] or
                        final_query_params.get('click_id', [None])[0] or
                        final_query_params.get('transaction_id', [None])[0] or
                        final_query_params.get('publisher_click_id', [None])[0] or
                        final_query_params.get('user_id', [None])[0] or
                        final_query_params.get('tracking_id', [None])[0] or
                        final_query_params.get('sub_id', [None])[0] or
                        final_query_params.get('sub1', [None])[0] or
                        final_query_params.get('sub2', [None])[0] or
                        final_query_params.get('sub3', [None])[0] or
                        final_query_params.get('sub4', [None])[0] or
                        final_query_params.get('sub5', [None])[0] or
                        final_query_params.get('aff_id', [None])[0] or
                        final_query_params.get('affiliate_id', [None])[0] or
                        final_query_params.get('campaign_id', [None])[0] or
                        final_query_params.get('source_id', [None])[0] or
                        final_query_params.get('external_id', [None])[0] or
                        # AppsFlyer specific
                        final_query_params.get('af_tranid', [None])[0] or
                        final_query_params.get('af_click_lookback', [None])[0] or
                        final_query_params.get('af_siteid', [None])[0] or
                        final_query_params.get('af_sub1', [None])[0] or
                        final_query_params.get('af_sub2', [None])[0] or
                        # Adjust specific
                        final_query_params.get('adj_t', [None])[0] or
                        final_query_params.get('adid', [None])[0])

                if tx_id:
                    found_in_url = final_url
                    # Determine MMP type based on final domain
                    domain = final_url_parsed.netloc.lower()
                    if any(pattern in domain for pattern in ["adjust.com", "adj.st", "adjust.io"]):
                        mmp_type = "Adjust (final URL)"
                    elif any(pattern in domain for pattern in ["appsflyer.com", "onelink.me", "app.appsflyer.com"]):
                        mmp_type = "AppsFlyer (final URL)"
                    elif any(pattern in domain for pattern in ["branch.io", "bnc.lt", "app.link"]):
                        mmp_type = "Branch (final URL)"
                    elif any(pattern in domain for pattern in ["ltv", "ltvplus", "ltv-mob"]):
                        mmp_type = "LTV Plus (final URL)"
                    elif any(pattern in domain for pattern in ["impact", "impactradius"]):
                        mmp_type = "Impact Radius (final URL)"
                    else:
                        mmp_type = f"Generic ({domain})"

        return {
            "tx_id": tx_id,
            "mmp_type": mmp_type,
            "final_url": final_url,
            "found_in_url": found_in_url,
            "redirect_chain": redirect_chain,
            "click_id": click_id
        }

    @Slot(object, object, QPushButton, QWidget, QVBoxLayout)
    def _ow_handle_txid_result(self, result_data, offer_data, button_to_hide, details_widget, details_layout):
        button_to_hide.setVisible(False) # Hide the "Fetch TxID" button
        details_widget.setVisible(True) # Show the details area

        tx_id = result_data['tx_id']
        mmp_type = result_data['mmp_type']
        final_url = result_data['final_url']
        found_in_url = result_data.get('found_in_url')
        redirect_chain = result_data.get('redirect_chain', [])
        click_id = result_data.get('click_id')

        self._clear_layout(details_layout) # Clear previous details if any

        # Display generated click ID
        if click_id:
            details_layout.addWidget(QLabel(f"<b>Generated Click ID:</b> <font color='#eee' style='background-color: #533483; padding: 4px; border-radius: 3px;'>{click_id}</font>"))

        if tx_id:
            details_layout.addWidget(QLabel(f"<b>Extracted Transaction ID:</b> <font color='#eee' style='background-color: #0f3460; padding: 4px; border-radius: 3px;'>{tx_id}</font>"))
            if found_in_url and found_in_url != final_url:
                found_url_label = QLabel(f"<b>Found in URL:</b> {found_in_url[:120]}{'...' if len(found_in_url) > 120 else ''}")
                found_url_label.setWordWrap(True)
                found_url_label.setStyleSheet("color: #e94560; font-weight: bold;")
                details_layout.addWidget(found_url_label)
            self._log_activity(f"Extracted TxID '{tx_id}' ({mmp_type}) for '{offer_data.get('name', 'N/A')}'", "SUCCESS")
        else:
            no_txid_label = QLabel("<b>Transaction ID not found in redirect chain or final URL.</b>")
            no_txid_label.setStyleSheet("color: #1a1a2e; background-color: #e94560; padding: 6px; border-radius: 4px; font-weight: bold;")
            details_layout.addWidget(no_txid_label)
            self._log_activity(f"No TxID found for '{offer_data.get('name', 'N/A')}' in redirect chain or final URL: {final_url}", "WARNING")

        # Enhanced MMP type display with color coding
        mmp_label = QLabel(f"<b>Detected MMP/Network:</b> {mmp_type}")
        if "Adjust" in mmp_type:
            mmp_label.setStyleSheet("color: #eee; background-color: #0f3460; padding: 6px; border-radius: 4px; font-weight: bold;")
        elif "AppsFlyer" in mmp_type:
            mmp_label.setStyleSheet("color: #eee; background-color: #533483; padding: 6px; border-radius: 4px; font-weight: bold;")
        elif "Branch" in mmp_type:
            mmp_label.setStyleSheet("color: #eee; background-color: #16213e; padding: 6px; border-radius: 4px; font-weight: bold;")
        else:
            mmp_label.setStyleSheet("color: #eee; background-color: #1a1a2e; padding: 6px; border-radius: 4px; font-weight: bold;")
        details_layout.addWidget(mmp_label)

        # Show redirect chain info with more details
        if len(redirect_chain) > 1:
            redirect_info_label = QLabel(f"<b>Redirect Chain:</b> {len(redirect_chain)} redirects processed")
            redirect_info_label.setStyleSheet("color: #533483; font-weight: bold;")
            details_layout.addWidget(redirect_info_label)

            # Show key domains in redirect chain
            domains = [urlparse(r['url']).netloc for r in redirect_chain if urlparse(r['url']).netloc]
            unique_domains = list(dict.fromkeys(domains))  # Preserve order, remove duplicates
            if len(unique_domains) > 1:
                domains_label = QLabel(f"<b>Domains in chain:</b> {' → '.join(unique_domains[:5])}")
                domains_label.setStyleSheet("color: #e94560; font-size: 9pt;")
                domains_label.setWordWrap(True)
                details_layout.addWidget(domains_label)

        final_url_label = QLabel(f"<b>Final Tracking Link:</b> {final_url[:120]}{'...' if len(final_url) > 120 else ''}")
        final_url_label.setWordWrap(True)
        details_layout.addWidget(final_url_label)

        # Enhanced MMP-specific notes and guidance
        if "AppsFlyer" in mmp_type:
            af_note = QLabel("📱 <b>AppsFlyer Detected:</b> This network typically uses Server-to-Server (S2S) postbacks. The generated Adjust-style callbacks below are for testing convenience only.")
            af_note.setStyleSheet("color: #1a1a2e; background-color: #e94560; padding: 8px; border-radius: 6px; font-style: italic; font-weight: 500;")
            af_note.setWordWrap(True)
            details_layout.addWidget(af_note)
        elif "Adjust" in mmp_type:
            adj_note = QLabel("🎯 <b>Adjust Detected:</b> This network supports both client-side callbacks and S2S postbacks. The generated callbacks below should work for testing.")
            adj_note.setStyleSheet("color: #eee; background-color: #0f3460; padding: 8px; border-radius: 6px; font-style: italic; font-weight: 500;")
            adj_note.setWordWrap(True)
            details_layout.addWidget(adj_note)
        elif "Branch" in mmp_type:
            branch_note = QLabel("🌿 <b>Branch Detected:</b> This deep linking platform may require webhook configuration for proper event tracking.")
            branch_note.setStyleSheet("color: #eee; background-color: #16213e; padding: 8px; border-radius: 6px; font-style: italic; font-weight: 500;")
            branch_note.setWordWrap(True)
            details_layout.addWidget(branch_note)
        elif "LTV Plus" in mmp_type:
            ltv_note = QLabel("📊 <b>LTV Plus Detected:</b> This performance network typically uses postback URLs for conversion tracking.")
            ltv_note.setStyleSheet("color: #eee; background-color: #533483; padding: 8px; border-radius: 6px; font-style: italic; font-weight: 500;")
            ltv_note.setWordWrap(True)
            details_layout.addWidget(ltv_note)

        goals = offer_data.get('goals', [])
        if tx_id and goals: # Only show goals if TxID was found for callback generation
            # Enhanced goals section with better styling
            goals_title = QLabel("🎯 <b>OFFER GOALS & EVENT CALLBACKS</b>")
            goals_title.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                               stop: 0 #e94560, stop: 1 #533483);
                    color: #eee;
                    padding: 12px;
                    border-radius: 8px;
                    font-size: 11pt;
                    font-weight: bold;
                    margin: 8px 0px;
                }
            """)
            details_layout.addWidget(goals_title)

            # Add summary of goals
            total_payout = sum(goal.get('currency', 0) for goal in goals)
            goals_summary = QLabel(f"📊 <b>Total Goals:</b> {len(goals)} | <b>Total Potential Payout:</b> {total_payout} points")
            goals_summary.setStyleSheet("color: #533483; font-weight: bold; margin-bottom: 10px;")
            details_layout.addWidget(goals_summary)

            # Create a more prominent goals display
            for i, goal in enumerate(goals):
                goal_frame = QFrame()
                goal_frame.setFrameShape(QFrame.Shape.StyledPanel)
                goal_frame.setStyleSheet("""
                    QFrame {
                        background-color: rgba(22, 33, 62, 0.5);
                        border: 2px solid #533483;
                        border-radius: 8px;
                        margin: 4px 0px;
                        padding: 8px;
                    }
                """)
                goal_layout = QVBoxLayout(goal_frame)
                goal_layout.setContentsMargins(8, 8, 8, 8)

                goal_text_orig = goal.get('name', goal.get('text', 'N/A'))
                goal_text_display = goal_text_orig.replace('TURBO EARNINGS', '<b><font color="#eee" style="background-color: #e94560; padding: 2px 4px; border-radius: 3px;">TURBO EARNINGS</font></b>')

                # Handle different payout formats
                payout_display = ""
                if 'payout_amount_micros' in goal:
                    payout = goal.get('payout_amount_micros', 0) / 1000000.0
                    payout_display = f"${payout:.2f}"
                elif 'currency' in goal:
                    payout_display = f"{goal['currency']} points"
                else:
                    payout_display = "Install only"

                goal_id = goal.get('goal_id', goal.get('id', 'N/A'))
                order_num = goal.get('orderNumber', i)

                # Enhanced goal header
                goal_header = QLabel(f"<b style='font-size: 11pt; color: #eee;'>#{order_num + 1}: {goal_text_display}</b>")
                goal_header.setWordWrap(True)
                goal_layout.addWidget(goal_header)

                # Goal details
                goal_details = QLabel(f"<b>Goal ID:</b> {goal_id} | <b>Payout:</b> {payout_display}")
                goal_details.setStyleSheet("color: #533483; font-weight: bold; margin: 4px 0px;")
                goal_layout.addWidget(goal_details)

                # Add time constraints if available
                if 'metadata' in goal:
                    metadata = goal['metadata']
                    time_info = []
                    if metadata.get('maxTime') and metadata.get('maxTime') != 'null':
                        max_time = metadata['maxTime']
                        time_metric = metadata.get('maxTimeMetric', 'days')
                        time_info.append(f"Complete within {max_time} {time_metric}")

                    if metadata.get('eventCategory'):
                        time_info.append(f"Category: {metadata['eventCategory']}")

                    if time_info:
                        time_label = QLabel(f"⏰ {' | '.join(time_info)}")
                        time_label.setStyleSheet("color: #e94560; font-size: 9pt; font-style: italic;")
                        time_label.setWordWrap(True)
                        goal_layout.addWidget(time_label)

                # Generate a unique click_id for each goal callback
                import uuid
                unique_goal_click_id = str(uuid.uuid4())

                # Display the unique click ID for this goal
                click_id_label = QLabel(f"<b>🔑 Unique Click ID:</b> <font color='#eee' style='background-color: #0f3460; padding: 4px 6px; border-radius: 4px; font-family: monospace; font-size: 9pt;'>{unique_goal_click_id}</font>")
                click_id_label.setWordWrap(True)
                goal_layout.addWidget(click_id_label)

                # Enhanced callback generation with more parameters
                callback_params = {
                    'kashkick_event': '1',
                    'goal_id': goal_id,
                    'transaction_id': tx_id,
                    'click_id': unique_goal_click_id,
                    'goal_name': goal_text_orig[:50],  # Truncated goal name
                    'payout': goal.get('currency', 0)
                }
                encoded_params = "&".join([f"{quote_plus(str(k))}={quote_plus(str(v))}" for k,v in callback_params.items() if v is not None])
                callback_url = f"https://your-callback-server.com/callback?{encoded_params}"
                generated_callback_url = f"{ADJUST_WRAPPER_URL}?click_callback={quote_plus(callback_url)}"

                # Enhanced link display with better styling
                self._create_enhanced_goal_link_display(goal_layout, f"🚀 Event Callback (Goal #{order_num + 1})", generated_callback_url, goal_id, payout_display)
                details_layout.addWidget(goal_frame)

            # Add "Fire All Goals" button if multiple goals
            if len(goals) > 1:
                fire_all_goals_button = QPushButton(f"🎯 Fire All {len(goals)} Goal Events (Sequential)")
                fire_all_goals_button.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 #533483, stop: 1 #e94560);
                        border: 2px solid #533483;
                        padding: 12px 20px;
                        min-height: 32px;
                        border-radius: 8px;
                        color: #eee;
                        font-weight: bold;
                        font-size: 10pt;
                        margin: 8px 0px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 #e94560, stop: 1 #533483);
                    }
                """)
                fire_all_goals_button.clicked.connect(lambda: self._fire_all_goal_callbacks(goals, tx_id))
                details_layout.addWidget(fire_all_goals_button)

        elif tx_id and not goals:
            no_goals_label = QLabel("ℹ️ <b>No specific goals found for this offer.</b><br>This might be a simple install offer or the goals data is not available.")
            no_goals_label.setStyleSheet("color: #533483; background-color: rgba(22, 33, 62, 0.5); padding: 12px; border-radius: 6px; font-weight: bold;")
            no_goals_label.setWordWrap(True)
            details_layout.addWidget(no_goals_label)
        elif not tx_id:
            no_txid_note = QLabel("⚠️ <b>Cannot generate goal callbacks without Transaction ID.</b><br>Please ensure the tracking link is accessible and contains valid tracking parameters.")
            no_txid_note.setStyleSheet("color: #eee; background-color: #e94560; padding: 12px; border-radius: 6px; font-weight: bold;")
            no_txid_note.setWordWrap(True)
            details_layout.addWidget(no_txid_note)


    @Slot(str, object, QPushButton, QVBoxLayout)
    def _ow_handle_txid_error(self, error_message, offer_data, button_to_hide, details_layout):
        button_to_hide.setText("Fetch Transaction ID (Retry)") # Change text back
        button_to_hide.setEnabled(True) # Re-enable button
        # Don't hide button, allow retry. Clear previous details if any.
        self._clear_layout(details_layout)

        err_label = QLabel(f"<b>Error fetching TxID:</b> {error_message}")
        err_label.setStyleSheet("color: #eee; background-color: #16213e; padding: 8px; border-radius: 6px; font-weight: bold;")
        err_label.setWordWrap(True)
        details_layout.insertWidget(0, err_label) # Insert error at the top of details section
        self._log_activity(f"Error fetching TxID for '{offer_data.get('name', 'N/A')}': {error_message}", "ERROR")


    # --- Helper Functions ---
    def _create_enhanced_goal_link_display(self, parent_layout, link_name, link_url, goal_id, payout_display):
        """Enhanced link display specifically for goal callbacks with better styling and functionality"""
        link_container = QFrame()
        link_container.setStyleSheet("""
            QFrame {
                background-color: rgba(15, 52, 96, 0.3);
                border: 1px solid #0f3460;
                border-radius: 6px;
                margin: 4px 0px;
                padding: 6px;
            }
        """)
        link_layout = QVBoxLayout(link_container)
        link_layout.setContentsMargins(8, 6, 8, 6)

        # Link header with goal info
        link_header = QLabel(f"<b>{link_name}</b> - {payout_display}")
        link_header.setStyleSheet("color: #eee; font-weight: bold; margin-bottom: 4px;")
        link_layout.addWidget(link_header)

        # URL display and action buttons
        url_actions_layout = QHBoxLayout()

        # URL text field (read-only)
        link_text = QLineEdit(link_url)
        link_text.setReadOnly(True)
        link_text.setStyleSheet("""
            QLineEdit {
                background-color: #1a1a2e;
                border: 1px solid #533483;
                padding: 6px;
                border-radius: 4px;
                color: #eee;
                font-family: monospace;
                font-size: 8pt;
            }
        """)
        url_actions_layout.addWidget(link_text, 1)

        # Copy button
        copy_button = QPushButton("📋 Copy")
        copy_button.setFixedWidth(80)
        copy_button.setStyleSheet("""
            QPushButton {
                background-color: #533483;
                border: 1px solid #0f3460;
                padding: 6px 12px;
                border-radius: 4px;
                color: #eee;
                font-weight: bold;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #0f3460;
            }
        """)
        copy_button.clicked.connect(lambda: self.copy_to_clipboard(link_url, f"Goal {goal_id} callback"))
        url_actions_layout.addWidget(copy_button)

        # Fire button
        fire_button = QPushButton(f"🚀 Fire Event")
        fire_button.setFixedWidth(100)
        fire_button.setStyleSheet("""
            QPushButton {
                background-color: #e94560;
                border: 1px solid #533483;
                padding: 6px 12px;
                border-radius: 4px;
                color: #eee;
                font-weight: bold;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #533483;
            }
        """)
        fire_button.clicked.connect(lambda: self.fire_link(link_url, f"Goal {goal_id} event"))
        url_actions_layout.addWidget(fire_button)

        link_layout.addLayout(url_actions_layout)
        parent_layout.addWidget(link_container)

    def _fire_all_goal_callbacks(self, goals, tx_id):
        """Fire all goal callbacks in sequence with proper delays"""
        if not goals or not tx_id:
            return

        self._log_activity(f"Starting to fire {len(goals)} goal callbacks sequentially.", "INFO")

        # Generate callback URLs for all goals
        callback_urls = []
        for goal in goals:
            import uuid
            unique_goal_click_id = str(uuid.uuid4())
            goal_id = goal.get('goal_id', goal.get('id', 'N/A'))
            goal_name = goal.get('name', goal.get('text', 'N/A'))

            callback_params = {
                'kashkick_event': '1',
                'goal_id': goal_id,
                'transaction_id': tx_id,
                'click_id': unique_goal_click_id,
                'goal_name': goal_name[:50],
                'payout': goal.get('currency', 0)
            }
            encoded_params = "&".join([f"{quote_plus(str(k))}={quote_plus(str(v))}" for k,v in callback_params.items() if v is not None])
            callback_url = f"https://your-callback-server.com/callback?{encoded_params}"
            generated_callback_url = f"{ADJUST_WRAPPER_URL}?click_callback={quote_plus(callback_url)}"
            callback_urls.append({
                'url': generated_callback_url,
                'goal_id': goal_id,
                'goal_name': goal_name
            })

        # Fire them sequentially using the existing worker
        self.sequential_fire_worker = GenericWorker(self._fire_goal_links_sequentially, callback_urls)
        self.sequential_fire_worker.start()

    def _fire_goal_links_sequentially(self, callback_data_list):
        """Fire goal callback links with minimal delays using direct HTTP requests - FAST!"""
        delay_between_fires = 0.5  # Reduced to 0.5 seconds for faster firing

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }

        for i, callback_data in enumerate(callback_data_list):
            url = callback_data['url']
            goal_id = callback_data['goal_id']
            goal_name = callback_data['goal_name']

            try:
                # Log detailed request information
                parsed_url = urlparse(url)
                query_params = parse_qs(parsed_url.query)

                # Extract and decode click_callback if present
                callback_data = "No callback data"
                if 'click_callback' in query_params:
                    callback_url = query_params['click_callback'][0]
                    callback_data = f"Callback URL: {callback_url}"

                    # Try to parse callback URL parameters
                    try:
                        callback_parsed = urlparse(callback_url)
                        callback_params = parse_qs(callback_parsed.query)
                        if callback_params:
                            param_details = []
                            for key, values in callback_params.items():
                                param_details.append(f"{key}={values[0] if values else 'None'}")
                            callback_data += f" | Params: {', '.join(param_details)}"
                    except:
                        pass

                # Extract click_id for logging
                click_id_info = ""
                if "click_id=" in url:
                    try:
                        click_id = query_params.get('click_id', [None])[0]
                        if click_id:
                            click_id_info = f" (Click ID: {click_id[:8]}...)"
                    except:
                        pass

                # Log the request being sent
                self._log_activity(
                    f"📤 SENDING Goal {goal_id} ({goal_name}) - {i+1}/{len(callback_data_list)}:\n"
                    f"  🎯 Target: {parsed_url.netloc}\n"
                    f"  📋 {callback_data[:150]}{'...' if len(callback_data) > 150 else ''}",
                    "INFO"
                )

                # Fire directly with requests for maximum speed
                response = requests.get(url, headers=headers, timeout=5, allow_redirects=True)

                self._log_activity(
                    f"✅ RESPONSE: Goal {goal_id} ({goal_name}) - {i+1}/{len(callback_data_list)}{click_id_info} "
                    f"(Status: {response.status_code}, Size: {len(response.content)} bytes)",
                    "SUCCESS"
                )

            except requests.exceptions.Timeout:
                self._log_activity(f"⏰ Timeout firing Goal {goal_id} ({goal_name}) - {i+1}/{len(callback_data_list)}", "ERROR")
            except requests.exceptions.RequestException as e:
                self._log_activity(f"❌ Error firing Goal {goal_id} ({goal_name}) - {i+1}/{len(callback_data_list)}: {str(e)[:50]}", "ERROR")

            # Short delay between requests to avoid overwhelming servers
            if i < len(callback_data_list) - 1:
                time.sleep(delay_between_fires)

        self._log_activity(f"🎯 Finished firing all {len(callback_data_list)} goal callbacks in {len(callback_data_list) * delay_between_fires:.1f}s!", "SUCCESS")
        return "Goal firing complete"

    def _clear_layout(self, layout):
        if layout is not None:
            while layout.count():
                item = layout.takeAt(0)
                widget = item.widget()
                if widget is not None:
                    widget.deleteLater()
                else:
                    sub_layout = item.layout()
                    if sub_layout is not None:
                        self._clear_layout(sub_layout)
                        # sub_layout.deleteLater() # Qt should handle this if parent is deleted

    def copy_to_clipboard(self, text_to_copy, item_name="Text"):
        try:
            clipboard = QApplication.clipboard()
            clipboard.setText(text_to_copy)
            self._log_activity(f"Copied {item_name} to clipboard: {text_to_copy[:70]}...", "SUCCESS")
            QMessageBox.information(self, "Copied", f"{item_name} copied to clipboard.")
        except Exception as e:
            self._log_activity(f"Failed to copy {item_name} to clipboard: {e}", "ERROR")
            QMessageBox.critical(self, "Copy Error", f"Could not copy to clipboard: {e}")

    def fire_link(self, url, link_name="Link"):
        """Fire a link by sending a GET request without opening browser - much faster!"""
        try:
            # Ensure URL has a scheme
            if not url.startswith(('http://', 'https://')):
                url_to_fire = 'https://' + url  # Default to https
            else:
                url_to_fire = url

            # Extract click_id from URL for enhanced logging
            click_id_info = ""
            if "click_id=" in url_to_fire:
                try:
                    parsed_url = urlparse(url_to_fire)
                    query_params = parse_qs(parsed_url.query)
                    click_id = query_params.get('click_id', [None])[0]
                    if click_id:
                        click_id_info = f" (Click ID: {click_id[:8]}...)"
                except:
                    pass

            # Use requests to fire the link instead of opening browser
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }

            # Log what we're about to send
            parsed_url = urlparse(url_to_fire)
            query_params = parse_qs(parsed_url.query)

            # Extract callback info for preview
            callback_preview = "No callback data"
            if 'click_callback' in query_params:
                callback_url = query_params['click_callback'][0]
                callback_preview = f"Callback URL: {callback_url[:80]}{'...' if len(callback_url) > 80 else ''}"

            self._log_activity(
                f"🚀 PREPARING TO FIRE {link_name}{click_id_info}:\n"
                f"  🎯 Target: {parsed_url.netloc}\n"
                f"  📋 {callback_preview}",
                "INFO"
            )

            # Start the request in a separate thread to avoid blocking UI
            self.fire_worker = GenericWorker(self._do_fire_request, url_to_fire, headers, link_name, click_id_info)
            self.fire_worker.result.connect(self._handle_fire_result)
            self.fire_worker.error.connect(self._handle_fire_error)
            self.fire_worker.start()

        except Exception as e:
            self._log_activity(f"Error preparing to fire {link_name} ({url}): {e}", "ERROR")

    def _do_fire_request(self, url, headers, link_name, click_id_info):
        """Execute the HTTP request to fire the link"""
        try:
            # Log the detailed request information
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)

            # Extract and decode click_callback if present
            callback_data = "No callback data"
            if 'click_callback' in query_params:
                callback_url = query_params['click_callback'][0]
                callback_data = f"Callback URL: {callback_url}"

                # Try to parse callback URL parameters
                try:
                    callback_parsed = urlparse(callback_url)
                    callback_params = parse_qs(callback_parsed.query)
                    if callback_params:
                        param_details = []
                        for key, values in callback_params.items():
                            param_details.append(f"{key}={values[0] if values else 'None'}")
                        callback_data += f"\nCallback Parameters: {', '.join(param_details)}"
                except:
                    pass

            # Log detailed request info
            self._log_activity(f"📤 SENDING REQUEST:\n  🎯 Target: {parsed_url.netloc}\n  📋 {callback_data}\n  🔗 Full URL: {url[:100]}{'...' if len(url) > 100 else ''}", "INFO")

            response = requests.get(url, headers=headers, timeout=10, allow_redirects=True)

            return {
                'success': True,
                'status_code': response.status_code,
                'url': url,
                'link_name': link_name,
                'click_id_info': click_id_info,
                'response_size': len(response.content) if response.content else 0,
                'callback_data': callback_data,
                'target_host': parsed_url.netloc
            }
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': 'Request timeout (10s)',
                'url': url,
                'link_name': link_name,
                'click_id_info': click_id_info
            }
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': str(e),
                'url': url,
                'link_name': link_name,
                'click_id_info': click_id_info
            }

    @Slot(object)
    def _handle_fire_result(self, result):
        """Handle successful link fire result"""
        if result['success']:
            self._log_activity(
                f"✅ RESPONSE RECEIVED:\n"
                f"  🎯 {result['link_name']}{result['click_id_info']}\n"
                f"  🌐 Host: {result.get('target_host', 'Unknown')}\n"
                f"  📊 Status: {result['status_code']} | Size: {result['response_size']} bytes\n"
                f"  📋 Data Sent: {result.get('callback_data', 'No callback data')[:100]}{'...' if len(result.get('callback_data', '')) > 100 else ''}",
                "SUCCESS"
            )
        else:
            self._log_activity(
                f"❌ FAILED TO SEND:\n"
                f"  🎯 {result['link_name']}{result['click_id_info']}\n"
                f"  ❌ Error: {result['error']}\n"
                f"  🔗 URL: {result['url'][:80]}{'...' if len(result['url']) > 80 else ''}",
                "ERROR"
            )

    @Slot(str)
    def _handle_fire_error(self, error_message):
        """Handle link fire error"""
        self._log_activity(f"❌ Fire request error: {error_message}", "ERROR")

    def closeEvent(self, event):
        # Clean up any running threads if necessary
        # For GenericWorker, if they are daemonic or parented to QObjects that get deleted,
        # they might terminate. Explicitly quitting them is safer.
        # This is a placeholder, actual thread management might be more complex
        # depending on how many persistent workers you might have.
        # For now, workers are short-lived.
        self._log_activity("Application closing.", "INFO")
        super().closeEvent(event)


# --- Main Execution ---
if __name__ == "__main__":
    app = QApplication(sys.argv)
    # You can set an application icon here if you have one
    # app_icon = QIcon("path/to/your/icon.png")
    # app.setWindowIcon(app_icon)
    
    window = MobileMarketingToolkit()
    window.show()
    sys.exit(app.exec())
